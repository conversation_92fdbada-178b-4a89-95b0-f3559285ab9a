import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

const LandingPage = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const buttonVariants = {
    hover: {
      scale: 1.05,
      transition: {
        duration: 0.2,
        ease: "easeInOut"
      }
    },
    tap: {
      scale: 0.95
    }
  };

  const buttons = [
    {
      title: "Software Engineering",
      link: "/software",
      description: "Full-stack and engineering work"
    },
    {
      title: "Game Development",
      link: "/games",
      description: "Gameplay, engine & backend"
    },
    {
      title: "Playground",
      link: "/playground",
      description: "Photography, videos & creative content"
    },
    {
      title: "Resume",
      link: "/resume",
      description: "Professional experience & qualifications"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-black to-zinc-900 text-white flex flex-col items-center justify-center p-4">
      <motion.div
        className="text-center max-w-6xl mx-auto"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Title */}
        <motion.h1
          className="text-4xl md:text-6xl lg:text-7xl font-bold mb-16 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent"
          variants={itemVariants}
        >
          Welcome to Yiran Li's Universe
        </motion.h1>

        {/* Button Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 max-w-6xl mx-auto"
          variants={itemVariants}
        >
          {buttons.map((button, index) => (
            <motion.div
              key={button.title}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
            >
              <Link
                to={button.link}
                className="block p-8 md:p-10 bg-zinc-800/50 backdrop-blur-sm border border-zinc-700/50 rounded-2xl hover:bg-zinc-700/50 transition-all duration-300 hover:ring-4 hover:ring-blue-500/30 hover:shadow-2xl hover:shadow-blue-500/20 group"
              >
                <h2 className="text-2xl md:text-3xl font-semibold mb-3 group-hover:text-blue-400 transition-colors duration-300">
                  {button.title}
                </h2>
                <p className="text-gray-400 text-lg group-hover:text-gray-300 transition-colors duration-300">
                  {button.description}
                </p>
                <div className="mt-4 flex justify-end">
                  <svg
                    className="w-6 h-6 text-gray-500 group-hover:text-blue-400 group-hover:translate-x-1 transition-all duration-300"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 8l4 4m0 0l-4 4m4-4H3"
                    />
                  </svg>
                </div>
              </Link>
            </motion.div>
          ))}
        </motion.div>
      </motion.div>
    </div>
  );
};

export default LandingPage;
