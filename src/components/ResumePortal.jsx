import { Link } from 'react-router-dom';

const MediaPodcasts = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-red-900 to-black text-white flex flex-col items-center justify-center p-4">
      <div className="text-center">
        <h1 className="text-5xl font-bold mb-8">Media & Podcasts</h1>
        <p className="text-xl text-gray-300 mb-8">Videos, audio and narrative</p>
        <Link
          to="/"
          className="inline-block px-6 py-3 bg-red-600 hover:bg-red-700 rounded-lg transition-colors duration-300"
        >
          ← Back to Home
        </Link>
      </div>
    </div>
  );
};

export default MediaPodcasts;
