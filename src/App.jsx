import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import LandingPage from './components/LandingPage';
import SoftwareEngineering from './components/SoftwareEngineering';
import GameDevelopment from './components/GameDevelopment';
import Playground from './components/Playground';
import Resume from './components/Resume';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<LandingPage />} />
        <Route path="/software" element={<SoftwareEngineering />} />
        <Route path="/games" element={<GameDevelopment />} />
        <Route path="/playground" element={<Playground />} />
        <Route path="/resume" element={<Resume />} />
      </Routes>
    </Router>
  );
}

export default App;
