import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import LandingPage from './components/LandingPage';
import GraphicsLab from './components/GraphicsLab';
import BackendForge from './components/BackendForge';
import ResumePortal from './components/ResumePortal';
import CodeProjects from './components/CodeProjects';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<LandingPage />} />
        <Route path="/graphics" element={<GraphicsLab />} />
        <Route path="/backend" element={<BackendForge />} />
        <Route path="/resume" element={<ResumePortal />} />
        <Route path="/projects" element={<CodeProjects />} />
      </Routes>
    </Router>
  );
}

export default App;
