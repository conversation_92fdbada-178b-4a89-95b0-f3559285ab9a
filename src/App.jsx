import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import LandingPage from './components/LandingPage';
import SoftwareEngineering from './components/SoftwareEngineering';
import GameDevelopment from './components/GameDevelopment';
import Photography from './components/Photography';
import MediaPodcasts from './components/MediaPodcasts';
import Resume from './components/Resume';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<LandingPage />} />
        <Route path="/code" element={<CodeProjects />} />
        <Route path="/graphics" element={<GraphicsProjects />} />
        <Route path="/games" element={<GameDevelopment />} />
        <Route path="/photography" element={<Photography />} />
        <Route path="/media" element={<MediaPodcasts />} />
        <Route path="/resume" element={<Resume />} />
      </Routes>
    </Router>
  );
}

export default App;
